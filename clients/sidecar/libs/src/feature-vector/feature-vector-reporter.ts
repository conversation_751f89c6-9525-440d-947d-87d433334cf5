import { MetricsReporter } from "../metrics/metrics-reporter";
import { getAPIClient } from "../client-interfaces/api-client";
import { getLogger } from "../logging";
import {
  FeatureVector,
  ClientSpecificData,
  createFeatures,
} from "./feature-vector-collector";

export class FeatureVectorReporter extends MetricsReporter<FeatureVector> {
  public static defaultMaxRecords = 10000;
  public static defaultBatchSize = 1;
  public static defaultUploadMsec = 10000;
  private _logger = getLogger("FeatureVectorReporter");

  constructor(maxRecords?: number, uploadMs?: number, batchSize?: number) {
    super(
      "FeatureVectorReporter",
      maxRecords ?? FeatureVectorReporter.defaultMaxRecords,
      uploadMs ?? FeatureVectorReporter.defaultUploadMsec,
      batchSize ?? FeatureVectorReporter.defaultBatchSize,
    );
  }

  // reportVector reports a feature vector.
  public async reportVector(clientData: ClientSpecificData): Promise<void> {
    const features = await createFeatures(clientData);
    super.report(features.toVector());
  }

  protected performUpload(batch: FeatureVector[]): Promise<void> {
    if (batch.length > 1) {
      this._logger.debug(`unexpected batch size: ${batch.length}`);
      return Promise.resolve();
    }
    return getAPIClient().logFeatureVector(batch[0]);
  }
}
