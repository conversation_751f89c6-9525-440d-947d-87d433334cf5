import { exec } from "child_process";
import * as crypto from "crypto";
import * as fs from "fs/promises";
import { machineIdSync } from "node-machine-id";
import * as os from "os";
import * as path from "path";
import * as si from "systeminformation";
import { promisify } from "util";

const execAsync = promisify(exec);

export type FeatureVector = Record<number, string>;

export enum FeatureVectorKey {
  vscode = 0,
  machineId = 1,
  os = 2,
  cpu = 3,
  memory = 4,
  numCpus = 5,
  hostname = 6,
  arch = 7,
  username = 8,
  macAddresses = 9,
  osRelease = 10,
  kernelVersion = 11,
  checksum = 12,
  telemetryDevDeviceId = 13,
  requestId = 14,
  randomHash = 15,
  osMachineId = 16,
  homeDirectoryIno = 17,
  projectRootIno = 18,
  gitUserEmail = 19,
  sshPublicKey = 20,
  gpuInfo = 21,
  timezone = 22,
  diskLayout = 23,
  systemInfo = 24,
  biosInfo = 25,
  baseboardInfo = 26,
  chassisInfo = 27,
}

export interface ClientSpecificData {
  clientVersion: string;
  clientMachineId: string;
  telemetryDevDeviceId: string;
  projectRootPath?: string;
}

export class Features {
  private _textEncoder = new TextEncoder();
  constructor(
    public readonly clientVersion: string,
    public readonly machineId: string,
    public readonly os: string,
    public readonly cpu: string,
    public readonly memory: string,
    public readonly numCpus: string,
    public readonly hostname: string,
    public readonly arch: string,
    public readonly username: string,
    public readonly macAddresses: string[],
    public readonly osRelease: string,
    public readonly kernelVersion: string,
    public readonly telemetryDevDeviceId: string,
    public readonly requestId: string,
    public readonly randomHash: string,
    public readonly osMachineId: string,
    public readonly homeDirectoryIno: string,
    public readonly projectRootIno: string,
    public readonly gitUserEmail: string,
    public readonly sshPublicKey: string,
    public readonly gpuInfo: string,
    public readonly timezone: string,
    public readonly diskLayout: string,
    public readonly systemInfo: string,
    public readonly biosInfo: string,
    public readonly baseboardInfo: string,
    public readonly chassisInfo: string,
  ) {}

  calculateChecksum(vector: FeatureVector) {
    const sortedKeys = Object.keys(vector).sort();
    const values = sortedKeys.map((key) => vector[Number(key)]);
    const combined = values.join("");
    const hash = sha256(this._textEncoder.encode(combined));

    return "v1#" + hash;
  }

  canonicalize(s: string): string {
    return sha256(this._textEncoder.encode(s.toLowerCase().trim()));
  }

  canonicalizeArray(array: string[]): string {
    return this.canonicalize(
      array.map((s) => s.toLowerCase().trim()).join(","),
    );
  }

  toVector() {
    const result: FeatureVector = {
      [FeatureVectorKey.vscode]: this.canonicalize(this.clientVersion),
      [FeatureVectorKey.machineId]: this.canonicalize(this.machineId),
      [FeatureVectorKey.os]: this.canonicalize(this.os),
      [FeatureVectorKey.cpu]: this.canonicalize(this.cpu),
      [FeatureVectorKey.memory]: this.canonicalize(this.memory),
      [FeatureVectorKey.numCpus]: this.canonicalize(this.numCpus),
      [FeatureVectorKey.hostname]: this.canonicalize(this.hostname),
      [FeatureVectorKey.arch]: this.canonicalize(this.arch),
      [FeatureVectorKey.username]: this.canonicalize(this.username),
      [FeatureVectorKey.macAddresses]: this.canonicalizeArray(
        this.macAddresses,
      ),
      [FeatureVectorKey.osRelease]: this.canonicalize(this.osRelease),
      [FeatureVectorKey.kernelVersion]: this.canonicalize(this.kernelVersion),
      [FeatureVectorKey.telemetryDevDeviceId]: this.canonicalize(
        this.telemetryDevDeviceId,
      ),
      [FeatureVectorKey.requestId]: this.canonicalize(this.requestId),
      [FeatureVectorKey.randomHash]: this.canonicalize(this.randomHash),
      [FeatureVectorKey.osMachineId]: this.canonicalize(this.osMachineId),
      [FeatureVectorKey.homeDirectoryIno]: this.canonicalize(
        this.homeDirectoryIno,
      ),
      [FeatureVectorKey.projectRootIno]: this.canonicalize(this.projectRootIno),
      [FeatureVectorKey.gitUserEmail]: this.canonicalize(this.gitUserEmail),
      [FeatureVectorKey.sshPublicKey]: this.canonicalize(this.sshPublicKey),
      [FeatureVectorKey.gpuInfo]: this.canonicalize(this.gpuInfo),
      [FeatureVectorKey.timezone]: this.canonicalize(this.timezone),
      [FeatureVectorKey.diskLayout]: this.canonicalize(this.diskLayout),
      [FeatureVectorKey.systemInfo]: this.canonicalize(this.systemInfo),
      [FeatureVectorKey.biosInfo]: this.canonicalize(this.biosInfo),
      [FeatureVectorKey.baseboardInfo]: this.canonicalize(this.baseboardInfo),
      [FeatureVectorKey.chassisInfo]: this.canonicalize(this.chassisInfo),
    };
    result[FeatureVectorKey.checksum] = this.calculateChecksum(result);
    return result;
  }
}

function sha256(data: Uint8Array): string {
  return crypto.createHash("sha256").update(data).digest("hex");
}

function getExternalMacAddresses(): string[] {
  const interfaces = os.networkInterfaces();
  let externalMacs: string[] = [];
  for (const iface in interfaces) {
    const ifaceInfos = interfaces[iface];
    if (!ifaceInfos) {
      continue;
    }
    if (ifaceInfos.length === 0) {
      continue;
    }
    if (ifaceInfos[0].internal) {
      continue;
    }
    for (const info of ifaceInfos) {
      externalMacs.push(info.mac);
    }
  }
  // Sort the external mac addresses to make the fingerprint stable
  externalMacs.sort();
  return externalMacs;
}

async function getInodeNumber(dirPath: string): Promise<string> {
  try {
    // Add timeout for file system operations (1 second should be plenty for local FS)
    const timeout = 1000;
    const statsPromise = fs.lstat(dirPath);

    const stats = await Promise.race([
      statsPromise,
      new Promise<never>((_, reject) =>
        setTimeout(() => reject(new Error("Timeout")), timeout),
      ),
    ]);

    return stats.ino.toString();
  } catch (e) {
    return "";
  }
}

async function getGitUserEmail(projectRootPath?: string): Promise<string> {
  try {
    if (!projectRootPath) {
      return "";
    }

    const cwd = projectRootPath;
    // Set a reasonable timeout for git commands (2 seconds)
    const timeout = 2000;

    // Get the git user email configured for this repository
    try {
      const { stdout: email } = await execAsync("git config user.email", {
        cwd,
        timeout,
      });
      return email.trim();
    } catch {
      // If local user.email is not set, try global
      try {
        const { stdout: globalEmail } = await execAsync(
          "git config --global user.email",
          {
            cwd,
            timeout,
          },
        );
        return globalEmail.trim();
      } catch {
        return "";
      }
    }
  } catch (e) {
    return "";
  }
}

function generateRandomHash(): string {
  return crypto.randomBytes(16).toString("hex");
}

async function getSshPublicKey(): Promise<string> {
  // Helper to read file with timeout
  const readFileWithTimeout = async (
    filePath: string,
    timeout: number,
  ): Promise<string> => {
    const readPromise = fs.readFile(filePath, "utf8");
    return Promise.race([
      readPromise,
      new Promise<string>((_, reject) =>
        setTimeout(() => reject(new Error("Timeout")), timeout),
      ),
    ]);
  };

  const timeout = 1000; // 1 second timeout for reading SSH keys
  const homeDir = os.homedir();

  try {
    const sshKeyPath = path.join(homeDir, ".ssh", "id_rsa.pub");
    const publicKey = await readFileWithTimeout(sshKeyPath, timeout);
    return publicKey.trim();
  } catch (e) {
    // Try other common key types
    try {
      const sshKeyPath = path.join(homeDir, ".ssh", "id_ed25519.pub");
      const publicKey = await readFileWithTimeout(sshKeyPath, timeout);
      return publicKey.trim();
    } catch {
      try {
        const sshKeyPath = path.join(homeDir, ".ssh", "id_ecdsa.pub");
        const publicKey = await readFileWithTimeout(sshKeyPath, timeout);
        return publicKey.trim();
      } catch {
        return "";
      }
    }
  }
}

async function getSystemInformation(): Promise<{
  gpuInfo: string;
  timezone: string;
  diskLayout: string;
  systemInfo: string;
  biosInfo: string;
  baseboardInfo: string;
  chassisInfo: string;
}> {
  try {
    // Add timeout to prevent hanging on system information collection
    const timeout = 10000; // 10 seconds for system info collection

    // Helper to add timeout to promises
    const withTimeout = <T>(
      promise: Promise<T>,
      timeoutMs: number,
    ): Promise<T> => {
      return Promise.race([
        promise,
        new Promise<T>((_, reject) =>
          setTimeout(() => reject(new Error("Timeout")), timeoutMs),
        ),
      ]);
    };

    // Get all system information in parallel for better performance with timeout
    // Wrap all calls in Promise.resolve to handle both sync and async returns
    const [graphics, time, diskLayout, system, bios, baseboard, chassis] =
      await Promise.all([
        withTimeout(Promise.resolve(si.graphics()), timeout),
        withTimeout(Promise.resolve(si.time()), timeout),
        withTimeout(Promise.resolve(si.diskLayout()), timeout),
        withTimeout(Promise.resolve(si.system()), timeout),
        withTimeout(Promise.resolve(si.bios()), timeout),
        withTimeout(Promise.resolve(si.baseboard()), timeout),
        withTimeout(Promise.resolve(si.chassis()), timeout),
      ]);

    // Extract GPU info
    const gpuInfo = graphics.controllers
      .map((gpu) =>
        `${gpu.vendor || ""} ${gpu.model || ""} ${gpu.vram || ""}MB`.trim(),
      )
      .filter(Boolean)
      .join("; ");

    // Get timezone
    const timezone = time.timezone || "";

    // Get disk layout info
    const diskLayoutInfo = diskLayout
      .map((disk) => `${disk.type || ""} ${disk.size || 0}`.trim())
      .filter(Boolean)
      .join("; ");

    // Get system info
    const systemInfo =
      `${system.manufacturer || ""} ${system.model || ""} ${system.version || ""}`.trim();

    // Get BIOS info
    const biosInfo =
      `${bios.vendor || ""} ${bios.version || ""} ${bios.releaseDate || ""}`.trim();

    // Get baseboard info
    const baseboardInfo =
      `${baseboard.manufacturer || ""} ${baseboard.model || ""} ${baseboard.version || ""}`.trim();

    // Get chassis info
    const chassisInfo =
      `${chassis.manufacturer || ""} ${chassis.type || ""} ${chassis.version || ""}`.trim();

    return {
      gpuInfo,
      timezone,
      diskLayout: diskLayoutInfo,
      systemInfo,
      biosInfo,
      baseboardInfo,
      chassisInfo,
    };
  } catch (e) {
    // Return empty strings if systeminformation fails
    return {
      gpuInfo: "",
      timezone: "",
      diskLayout: "",
      systemInfo: "",
      biosInfo: "",
      baseboardInfo: "",
      chassisInfo: "",
    };
  }
}

export async function createFeatures(
  clientData: ClientSpecificData,
): Promise<Features> {
  const cpus = os.cpus();
  let username = "";
  try {
    username = os.userInfo().username;
  } catch (e) {
    // Ignore
  }

  // Generate a request ID
  const requestId = crypto.randomUUID();

  // Generate a random hash
  const randomHash = generateRandomHash();

  // Get OS machine ID from node-machine-id
  let osMachineId = "";
  try {
    osMachineId = machineIdSync();
  } catch (e) {
    // Fallback to empty string if we can't get the machine ID
    osMachineId = "";
  }

  // Get home directory inode
  const homeDir = os.homedir();
  const homeDirectoryIno = await getInodeNumber(homeDir);

  // Get project root inode
  const projectRootIno = clientData.projectRootPath
    ? await getInodeNumber(clientData.projectRootPath)
    : "";

  // Get git user email from repository config
  const gitUserEmail = await getGitUserEmail(clientData.projectRootPath);

  // Get SSH public key
  const sshPublicKey = await getSshPublicKey();

  // Get system information from systeminformation package
  const sysInfo = await getSystemInformation();

  return new Features(
    clientData.clientVersion,
    clientData.clientMachineId,
    os.type(),
    cpus[0].model,
    os.totalmem().toString(),
    cpus.length.toString(),
    os.hostname(),
    os.machine(),
    username,
    getExternalMacAddresses(),
    os.release(),
    os.version(),
    clientData.telemetryDevDeviceId,
    requestId,
    randomHash,
    osMachineId,
    homeDirectoryIno,
    projectRootIno,
    gitUserEmail,
    sshPublicKey,
    sysInfo.gpuInfo,
    sysInfo.timezone,
    sysInfo.diskLayout,
    sysInfo.systemInfo,
    sysInfo.biosInfo,
    sysInfo.baseboardInfo,
    sysInfo.chassisInfo,
  );
}
