import {
    ClientSpecificData,
    createFeatures,
} from "@augment-internal/sidecar-libs/src/feature-vector/feature-vector-collector";
import * as vscode from "vscode";

import { APIServer } from "../augment-api";

export class SidecarFeatureVectorClient {
    constructor(
        private _apiServer: APIServer,
        private _context: vscode.ExtensionContext
    ) {}

    async collectAndReportFeatureVector(): Promise<void> {
        // Collect client-specific data from VSCode
        const clientData: ClientSpecificData = {
            clientVersion: vscode.version,
            clientMachineId: vscode.env.machineId,
            telemetryDevDeviceId:
                this._context.globalState.get<string>("telemetry.devDeviceId") || "",
            projectRootPath: vscode.workspace.workspaceFolders?.[0]?.uri.fsPath,
        };

        // Use the sidecar library to collect features
        const features = await createFeatures(clientData);
        const featureVector = features.toVector();

        // Report the feature vector using the existing API
        await this._apiServer.logFeatureVector(featureVector);
    }
}
